C:\msys64\mingw64\bin\cmake.exe -E rm -f CMakeFiles\test_minio.dir/objects.a
C:\msys64\mingw64\bin\ar.exe qc CMakeFiles\test_minio.dir/objects.a @CMakeFiles\test_minio.dir\objects1.rsp
C:\msys64\mingw64\bin\g++.exe -static -Wall -Wextra -O2 -D_WIN32_WINNT=0x0601 -Wall -Wextra -O2 -O3 -DNDEBUG -static -static-libgcc -static-libstdc++ -Wl,--whole-archive CMakeFiles\test_minio.dir/objects.a -Wl,--no-whole-archive -o test_minio.exe -Wl,--out-implib,libtest_minio.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\test_minio.dir\linkLibs.rsp
