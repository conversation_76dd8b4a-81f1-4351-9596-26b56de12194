
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "C:/work/code/java/data-annotation-platform/native/src/minio_client.cpp" "CMakeFiles/test_minio.dir/src/minio_client.cpp.obj" "gcc" "CMakeFiles/test_minio.dir/src/minio_client.cpp.obj.d"
  "C:/work/code/java/data-annotation-platform/native/test-minio.cpp" "CMakeFiles/test_minio.dir/test-minio.cpp.obj" "gcc" "CMakeFiles/test_minio.dir/test-minio.cpp.obj.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
